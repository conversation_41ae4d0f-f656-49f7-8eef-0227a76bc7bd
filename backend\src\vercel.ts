/**
 * Vercel Serverless Function Entry Point
 * This file exports the Express app for Vercel serverless deployment
 * without creating an HTTP server instance or starting background services
 */

import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import { logger } from './utils/logger';
import { enforceHttps, secureHeadersMiddleware } from './middleware/enforceHttps';
import authRoutes from './features/auth/routes';
import profileRoutes from './features/profile/routes';
import walletRoutes from './features/wallet/routes';
import rewardsRoutes from './features/rewards/routes';
import tournamentRoutes from './features/tournaments/routes';
import matchRoutes from './features/matches/routes';
import adminUsersRoutes from './features/admin-users/routes';
import adminWithdrawalRoutes from './features/wallet/routes/adminWithdrawalRoutes';
import adminNotificationRoutes from './features/wallet/routes/adminNotificationRoutes';
import gameRoutes from './routes/games';
import supportRoutes from './features/support/routes';

// Import seed data for tournaments (but don't start server)
import './features/tournaments/services/seedTournaments';

// Log that we're running in serverless mode
logger.info('WiggyZ backend starting in Vercel serverless mode');

// Create Express app for serverless deployment
const app = express();

// Middleware - same as main app but without Redis initialization and background services

// Apply HTTPS enforcement early in the middleware chain
app.use(enforceHttps);

// Apply secure headers middleware
app.use(secureHeadersMiddleware);

// Body parsing middleware with explicit charset
app.use(express.json({
  limit: '10mb',
  type: 'application/json'
}));

// Set default charset for all responses
app.use((req, res, next) => {
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  next();
});

// Add middleware to log all OPTIONS requests for debugging
app.use((req, res, next) => {
  if (req.method === 'OPTIONS') {
    logger.debug(`🔍 [CORS] OPTIONS request to ${req.originalUrl}`);
    logger.debug(`🔍 [CORS] Origin: ${req.headers.origin}`);
    logger.debug(`🔍 [CORS] Access-Control-Request-Method: ${req.headers['access-control-request-method']}`);
    logger.debug(`🔍 [CORS] Access-Control-Request-Headers: ${req.headers['access-control-request-headers']}`);
  }
  next();
});

// Configure CORS with specific options
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://app.wiggyz.com', 'https://admin.wiggyz.com'] // Restrict origins in production
    : '*', // Allow all origins in development
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Idempotency-Key'],
  credentials: true, // Support credentials
  maxAge: 0 // Disable preflight caching for debugging
}));

// Log all requests
app.use(morgan('dev'));

// Add route debugging for tournaments
app.use('/api/v1/tournaments', (req, res, next) => {
  console.log(`Tournament route accessed: ${req.method} ${req.path}`);
  next();
});

// Routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/profile', profileRoutes);
app.use('/api/v1/wallet', walletRoutes);
app.use('/api/v1/rewards', rewardsRoutes);
app.use('/api/v1/tournaments', tournamentRoutes);
app.use('/api/v1/matches', matchRoutes);
app.use('/api/v1/admin/users', adminUsersRoutes);
app.use('/api/v1/admin/withdrawals', adminWithdrawalRoutes);
app.use('/api/v1/admin/notifications', adminNotificationRoutes);
app.use('/api/v1/games', gameRoutes);
app.use('/api/v1/support', supportRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    environment: 'serverless',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'Internal Server Error',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Export the Express app for Vercel
export default app;
