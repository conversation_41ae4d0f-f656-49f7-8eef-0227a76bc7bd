/**
 * Vercel Serverless Function Entry Point
 * This file exports the Express app for Vercel serverless deployment
 * without creating an HTTP server instance
 */

import app from './app';
import { logger } from './utils/logger';

// Import seed data for tournaments (but don't start server)
import './features/tournaments/services/seedTournaments';

// Log that we're running in serverless mode
logger.info('WiggyZ backend starting in Vercel serverless mode');

// Export the Express app for Vercel
export default app;
